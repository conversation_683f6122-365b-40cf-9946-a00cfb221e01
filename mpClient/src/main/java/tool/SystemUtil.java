package tool;

import com.google.common.primitives.Bytes;
import org.apache.commons.codec.digest.DigestUtils;
import oshi.SystemInfo;
import oshi.hardware.*;

import java.io.*;
import java.lang.management.ManagementFactory;
import java.lang.management.OperatingSystemMXBean;
import java.lang.management.RuntimeMXBean;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.rmi.server.ExportException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Scanner;


public class SystemUtil {
    public static final long SECOND = 1000;
    public static final long MINUTE = 60 * 1000;
    public static final long HOUR = 60 * 60 * 1000;
    public static final long DAY = 12 * 60 * 60 * 1000;
    public static final int MIN_DELAY = 900;
    public static final int MAX_DELAY = 2000;

    // 机器码缓存，避免重复计算
    private static String cachedMachineCode = null;

    public static boolean isNotEmpty(Object object) {
        return object != null;
    }

    public static boolean isEmpty(Object object) {
        return object == null;
    }

    public static void delay(long time) {
        try {
            Thread.sleep(time);
        } catch (InterruptedException e) {
            Thread.currentThread().stop();
        }
    }

    public static void delayNoCatch(long time) throws InterruptedException {
        Thread.sleep(time);
    }

    public static void randomDelay() throws InterruptedException {
        randomDelay(MIN_DELAY, MAX_DELAY);
    }

    public static void randomDelay(int min, int max) throws InterruptedException {
        int random = RandomUtil.random(min, max);
        System.out.println(random);
        Thread.sleep(random);
    }

    /**
     * 用来输出jar里面的文件到指定目录
     *
     * @param input
     * @param output
     * @throws IOException
     */
    public static void outPutFile(InputStream input, OutputStream output) throws IOException {
        if (input == null || output == null) {
            throw new ExportException("input or output Stream in Null.");
        }
        try {
            byte[] buf = new byte[1024];
            int bytesRead;
            while ((bytesRead = input.read(buf)) > 0) {
                output.write(buf, 0, bytesRead);
            }
        } finally {
            input.close();
            output.close();
        }
    }

    /**
     * 输出文件到jre目录
     */
    public static void outPutFileToSystemDir(InputStream inputStream, String fileName) {
        String outputPath = System.getProperty("user.dir")
                + File.separator + "bin" + File.separator + "config" + File.separator + fileName;
        File outputFile = new File(outputPath);
        outPutFileToTargetDir(inputStream, outputFile);
    }

    /**
     * 输出文件到指定目录
     */
    public static void outPutFileToTargetDir(InputStream inputStream, File outputFile) {
        try {
            OutputStream outputStream = new FileOutputStream(outputFile);
            outPutFile(inputStream, outputStream);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static String readFromFile(String path) {
        return readFromFile(new File(path));
    }

    //读文件
    public static String readFromFile(File file) {
        if (!file.exists()) {
            return "";
        }
        InputStream inputStream = null;
        try {
            inputStream = new FileInputStream(file);
            List<Byte> iis = new ArrayList<>();
            int len = 0, temp = 0;
            while ((temp = inputStream.read()) != -1) {
                iis.add((byte) (temp));
                len++;
            }
            byte[] bytes = Bytes.toArray(iis);
            inputStream.close();
            return new String(bytes, "utf-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    // 获取网卡地址
    public static String getMacAddress() {
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                byte[] mac = networkInterface.getHardwareAddress();
                if (mac != null) {
                    StringBuilder macAddress = new StringBuilder();
                    for (byte b : mac) {
                        macAddress.append(String.format("%02X:", b));
                    }
                    if (macAddress.length() > 0) {
                        macAddress.deleteCharAt(macAddress.length() - 1);
                    }
                    return macAddress.toString();
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        return null;
    }

    // 获取硬盘序列化
    public static String getHardDiskSerialNumber() {
        String os = System.getProperty("os.name").toLowerCase();
        try {
            if (os.contains("win")) {
                String command = "wmic diskdrive get serialnumber";
                Process process = Runtime.getRuntime().exec(command);
                process.getOutputStream().close();
                Scanner scanner = new Scanner(process.getInputStream());
                String serial = scanner.next();
                scanner.close();
                return serial;
            } else if (os.contains("mac") || os.contains("nix") || os.contains("nux") || os.contains("mac os x")) {
                String command = "ioreg -rd1 -c IOPlatformExpertDevice | awk '/IOPlatformSerialNumber/ {print $3;}'";
                Process process = Runtime.getRuntime().exec(new String[]{"/bin/sh", "-c", command});
                process.getOutputStream().close();
                Scanner scanner = new Scanner(process.getInputStream());
                String serial = scanner.next();
                scanner.close();
                return serial;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    // 获取cpu序列号
    public static String getCpuSerial() {
        String serial = null;
        try {
            OperatingSystemMXBean operatingSystemMXBean = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
            String os = operatingSystemMXBean.getName();
            if (os.contains("Windows")) {
                Process process = Runtime.getRuntime().exec(new String[]{"wmic", "cpu", "get", "ProcessorId"});
                process.getOutputStream().close();
                Scanner scanner = new Scanner(process.getInputStream());
                scanner.next();
                serial = scanner.next();
                scanner.close();
            } else if (os.contains("Mac")) {
                Process process = Runtime.getRuntime().exec(new String[]{"/usr/sbin/system_profiler", "SPHardwareDataType"});
                process.getOutputStream().close();
                Scanner scanner = new Scanner(process.getInputStream());
                while (scanner.hasNext()) {
                    String line = scanner.nextLine().trim();
                    if (line.startsWith("Serial Number")) {
                        serial = line.split(":")[1].trim();
                        break;
                    }
                }
                scanner.close();
            } else if (os.contains("Linux")) {
                Process process = Runtime.getRuntime().exec(new String[]{"dmidecode", "-t", "system"});
                process.getOutputStream().close();
                Scanner scanner = new Scanner(process.getInputStream());
                while (scanner.hasNext()) {
                    String line = scanner.nextLine().trim();
                    if (line.startsWith("Serial Number")) {
                        serial = line.split(":")[1].trim();
                        break;
                    }
                }
                scanner.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return serial;
    }

    /**
     * 获取可靠的机器码 - 主要方法
     * 使用多层次备用方案，确保100%能获取到唯一的机器码
     *
     * @return 32位十六进制机器码字符串
     */
    public static String getReliableMachineCode() {
        // 如果已经缓存，直接返回
        if (cachedMachineCode != null && !cachedMachineCode.isEmpty()) {
            return cachedMachineCode;
        }

        StringBuilder machineCode = new StringBuilder();

        try {
            // 第一优先级：硬件指纹（使用OSHI库）
            String hardwareFingerprint = getHardwareFingerprint();
            if (hardwareFingerprint != null && !hardwareFingerprint.isEmpty()) {
                machineCode.append("HW:").append(hardwareFingerprint);
            }

            // 第二优先级：可靠的MAC地址
            String macAddress = getReliableMacAddress();
            if (macAddress != null && !macAddress.isEmpty()) {
                machineCode.append("MAC:").append(macAddress);
            }

            // 第三优先级：系统指纹
            String systemFingerprint = getSystemFingerprint();
            if (systemFingerprint != null && !systemFingerprint.isEmpty()) {
                machineCode.append("SYS:").append(systemFingerprint);
            }

            // 第四优先级：JVM运行时信息
            String jvmFingerprint = getJVMFingerprint();
            if (jvmFingerprint != null && !jvmFingerprint.isEmpty()) {
                machineCode.append("JVM:").append(jvmFingerprint);
            }

            // 最后备用：基于Java环境的相对稳定信息
            if (machineCode.length() == 0) {
                machineCode.append("FALLBACK:")
                        .append(System.getProperty("java.home", "").hashCode())
                        .append(System.getProperty("os.name", ""))
                        .append(Runtime.getRuntime().availableProcessors())
                        .append(Runtime.getRuntime().maxMemory());
            }

            // 生成SHA-256哈希作为最终机器码
            cachedMachineCode = generateSHA256Hash(machineCode.toString());
            return cachedMachineCode;

        } catch (Exception e) {
            e.printStackTrace();
            // 异常情况下的最终备用方案（使用相对稳定的信息）
            String fallback = "ERROR:" +
                            System.getProperty("java.home", "").hashCode() + ":" +
                            System.getProperty("os.name", "") + ":" +
                            Runtime.getRuntime().availableProcessors();
            cachedMachineCode = generateSHA256Hash(fallback);
            return cachedMachineCode;
        }
    }

    /**
     * 使用OSHI库获取硬件指纹
     *
     * @return 硬件指纹字符串
     */
    private static String getHardwareFingerprint() {
        try {
            SystemInfo systemInfo = new SystemInfo();
            HardwareAbstractionLayer hardware = systemInfo.getHardware();
            StringBuilder fingerprint = new StringBuilder();

            // 主板信息
            ComputerSystem computerSystem = hardware.getComputerSystem();
            String manufacturer = computerSystem.getManufacturer();
            String model = computerSystem.getModel();
            String serialNumber = computerSystem.getSerialNumber();

            if (manufacturer != null && !manufacturer.trim().isEmpty()) {
                fingerprint.append("MANU:").append(manufacturer.trim());
            }
            if (model != null && !model.trim().isEmpty()) {
                fingerprint.append("MODEL:").append(model.trim());
            }
            if (serialNumber != null && !serialNumber.trim().isEmpty() &&
                    !serialNumber.equalsIgnoreCase("unknown") &&
                    !serialNumber.equalsIgnoreCase("to be filled by o.e.m.")) {
                fingerprint.append("SERIAL:").append(serialNumber.trim());
            }

            // 主板信息
            Baseboard baseboard = computerSystem.getBaseboard();
            String baseboardManufacturer = baseboard.getManufacturer();
            String baseboardModel = baseboard.getModel();
            String baseboardSerial = baseboard.getSerialNumber();

            if (baseboardManufacturer != null && !baseboardManufacturer.trim().isEmpty()) {
                fingerprint.append("BMANU:").append(baseboardManufacturer.trim());
            }
            if (baseboardModel != null && !baseboardModel.trim().isEmpty()) {
                fingerprint.append("BMODEL:").append(baseboardModel.trim());
            }
            if (baseboardSerial != null && !baseboardSerial.trim().isEmpty() &&
                    !baseboardSerial.equalsIgnoreCase("unknown")) {
                fingerprint.append("BSERIAL:").append(baseboardSerial.trim());
            }

            // CPU信息
            CentralProcessor processor = hardware.getProcessor();
            CentralProcessor.ProcessorIdentifier processorId = processor.getProcessorIdentifier();
            String processorIdentifier = processorId.getIdentifier();
            String processorID = processorId.getProcessorID();

            if (processorIdentifier != null && !processorIdentifier.trim().isEmpty()) {
                fingerprint.append("CPUID:").append(processorIdentifier.trim());
            }
            if (processorID != null && !processorID.trim().isEmpty()) {
                fingerprint.append("CPUPID:").append(processorID.trim());
            }

            // 内存总量
            long totalMemory = hardware.getMemory().getTotal();
            fingerprint.append("MEM:").append(totalMemory);

            return fingerprint.length() > 0 ? fingerprint.toString() : null;

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取可靠的MAC地址（改进版）
     * 过滤虚拟网卡，选择最稳定的物理网卡
     *
     * @return MAC地址字符串
     */
    private static String getReliableMacAddress() {
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            List<String> macAddresses = new ArrayList<>();

            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface ni = networkInterfaces.nextElement();
                byte[] mac = ni.getHardwareAddress();

                // 过滤条件：
                // 1. MAC地址不为空且长度为6字节
                // 2. 不是回环接口
                // 3. 不是虚拟接口
                // 4. 接口是启用状态
                if (mac != null && mac.length == 6 &&
                        !ni.isLoopback() && !ni.isVirtual() && ni.isUp()) {

                    StringBuilder sb = new StringBuilder();
                    for (byte b : mac) {
                        sb.append(String.format("%02X", b));
                    }
                    String macStr = sb.toString();

                    // 过滤掉明显的虚拟MAC地址
                    if (!isVirtualMac(macStr)) {
                        macAddresses.add(macStr);
                    }
                }
            }

            // 如果没有找到合适的MAC地址，尝试获取任何可用的MAC地址
            if (macAddresses.isEmpty()) {
                networkInterfaces = NetworkInterface.getNetworkInterfaces();
                while (networkInterfaces.hasMoreElements()) {
                    NetworkInterface ni = networkInterfaces.nextElement();
                    byte[] mac = ni.getHardwareAddress();
                    if (mac != null && mac.length == 6) {
                        StringBuilder sb = new StringBuilder();
                        for (byte b : mac) {
                            sb.append(String.format("%02X", b));
                        }
                        macAddresses.add(sb.toString());
                    }
                }
            }

            // 返回字典序最小的MAC地址，保证稳定性
            return macAddresses.stream().min(String::compareTo).orElse(null);

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 判断是否为虚拟MAC地址
     *
     * @param mac MAC地址字符串
     * @return 是否为虚拟MAC地址
     */
    private static boolean isVirtualMac(String mac) {
        if (mac == null || mac.length() != 12) {
            return true;
        }

        // 常见的虚拟MAC地址前缀
        String[] virtualPrefixes = {
                "000000", "FFFFFFFFFFFF", "005056", "000C29", "001C14", "080027"
        };

        for (String prefix : virtualPrefixes) {
            if (mac.startsWith(prefix)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取系统硬件指纹（只包含稳定的硬件特征）
     *
     * @return 系统硬件指纹字符串
     */
    private static String getSystemFingerprint() {
        try {
            StringBuilder fingerprint = new StringBuilder();

            // 只使用稳定的系统信息
            String osArch = System.getProperty("os.arch", "");
            if (!osArch.isEmpty()) {
                fingerprint.append("ARCH:").append(osArch);
            }

            // 处理器核心数（相对稳定的硬件特征）
            fingerprint.append("CORES:").append(Runtime.getRuntime().availableProcessors());

            // 最大内存（硬件特征）
            fingerprint.append("MAXMEM:").append(Runtime.getRuntime().maxMemory());

            // 文件系统总容量（相对稳定的硬件特征）
            File[] roots = File.listRoots();
            for (File root : roots) {
                long totalSpace = root.getTotalSpace();
                if (totalSpace > 0) {
                    fingerprint.append("DISK:").append(totalSpace);
                }
            }

            return fingerprint.length() > 0 ? fingerprint.toString() : null;

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取稳定的系统标识（基于主机名等相对稳定的信息）
     *
     * @return 系统标识字符串
     */
    private static String getJVMFingerprint() {
        try {
            StringBuilder fingerprint = new StringBuilder();

            // 获取主机名（相对稳定）
            RuntimeMXBean runtimeMXBean = ManagementFactory.getRuntimeMXBean();
            String jvmName = runtimeMXBean.getName(); // 格式: pid@hostname

            if (jvmName != null && jvmName.contains("@")) {
                String hostname = jvmName.split("@")[1];
                if (hostname != null && !hostname.isEmpty()) {
                    fingerprint.append("HOST:").append(hostname);
                }
            }

            // 操作系统名称（稳定）
            String osName = System.getProperty("os.name", "");
            if (!osName.isEmpty()) {
                fingerprint.append("OS:").append(osName);
            }

            return fingerprint.length() > 0 ? fingerprint.toString() : null;

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 生成SHA-256哈希
     *
     * @param input 输入字符串
     * @return 32位十六进制哈希字符串
     */
    private static String generateSHA256Hash(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder();

            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString().toUpperCase();

        } catch (NoSuchAlgorithmException | java.io.UnsupportedEncodingException e) {
            e.printStackTrace();
            // 如果SHA-256不可用，使用MD5作为备用
            return DigestUtils.md5Hex(input).toUpperCase();
        }
    }

    /**
     * 清除机器码缓存（用于测试或重新生成）
     */
    public static void clearMachineCodeCache() {
        cachedMachineCode = null;
    }

    /**
     * 获取机器码详细信息（用于调试）
     *
     * @return 包含各个组件信息的详细字符串
     */
    public static String getMachineCodeDetails() {
        StringBuilder details = new StringBuilder();

        details.append("=== 机器码详细信息 ===\n");

        String hardwareFingerprint = getHardwareFingerprint();
        details.append("硬件指纹: ").append(hardwareFingerprint != null ? hardwareFingerprint : "获取失败").append("\n");

        String macAddress = getReliableMacAddress();
        details.append("MAC地址: ").append(macAddress != null ? macAddress : "获取失败").append("\n");

        String systemFingerprint = getSystemFingerprint();
        details.append("系统指纹: ").append(systemFingerprint != null ? systemFingerprint : "获取失败").append("\n");

        String jvmFingerprint = getJVMFingerprint();
        details.append("系统标识: ").append(jvmFingerprint != null ? jvmFingerprint : "获取失败").append("\n");

        String machineCode = getReliableMachineCode();
        details.append("最终机器码: ").append(machineCode).append("\n");

        return details.toString();
    }

}
