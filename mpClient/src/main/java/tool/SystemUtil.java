package tool;

import com.google.common.primitives.Bytes;
import org.apache.commons.codec.digest.DigestUtils;
import oshi.SystemInfo;
import oshi.hardware.*;

import java.io.*;
import java.lang.management.ManagementFactory;
import java.lang.management.OperatingSystemMXBean;
import java.lang.management.RuntimeMXBean;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.rmi.server.ExportException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Scanner;


public class SystemUtil {
    public static final long SECOND = 1000;
    public static final long MINUTE = 60 * 1000;
    public static final long HOUR = 60 * 60 * 1000;
    public static final long DAY = 12 * 60 * 60 * 1000;
    public static final int MIN_DELAY = 900;
    public static final int MAX_DELAY = 2000;

    // 机器码缓存，避免重复计算
    private static String cachedMachineCode = null;

    public static boolean isNotEmpty(Object object) {
        return object != null;
    }

    public static boolean isEmpty(Object object) {
        return object == null;
    }

    public static void delay(long time) {
        try {
            Thread.sleep(time);
        } catch (InterruptedException e) {
            Thread.currentThread().stop();
        }
    }

    public static void delayNoCatch(long time) throws InterruptedException {
        Thread.sleep(time);
    }

    public static void randomDelay() throws InterruptedException {
        randomDelay(MIN_DELAY, MAX_DELAY);
    }

    public static void randomDelay(int min, int max) throws InterruptedException {
        int random = RandomUtil.random(min, max);
        System.out.println(random);
        Thread.sleep(random);
    }

    /**
     * 用来输出jar里面的文件到指定目录
     *
     * @param input
     * @param output
     * @throws IOException
     */
    public static void outPutFile(InputStream input, OutputStream output) throws IOException {
        if (input == null || output == null) {
            throw new ExportException("input or output Stream in Null.");
        }
        try {
            byte[] buf = new byte[1024];
            int bytesRead;
            while ((bytesRead = input.read(buf)) > 0) {
                output.write(buf, 0, bytesRead);
            }
        } finally {
            input.close();
            output.close();
        }
    }

    /**
     * 输出文件到jre目录
     */
    public static void outPutFileToSystemDir(InputStream inputStream, String fileName) {
        String outputPath = System.getProperty("user.dir")
                + File.separator + "bin" + File.separator + "config" + File.separator + fileName;
        File outputFile = new File(outputPath);
        outPutFileToTargetDir(inputStream, outputFile);
    }

    /**
     * 输出文件到指定目录
     */
    public static void outPutFileToTargetDir(InputStream inputStream, File outputFile) {
        try {
            OutputStream outputStream = new FileOutputStream(outputFile);
            outPutFile(inputStream, outputStream);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static String readFromFile(String path) {
        return readFromFile(new File(path));
    }

    //读文件
    public static String readFromFile(File file) {
        if (!file.exists()) {
            return "";
        }
        InputStream inputStream = null;
        try {
            inputStream = new FileInputStream(file);
            List<Byte> iis = new ArrayList<>();
            int len = 0, temp = 0;
            while ((temp = inputStream.read()) != -1) {
                iis.add((byte) (temp));
                len++;
            }
            byte[] bytes = Bytes.toArray(iis);
            inputStream.close();
            return new String(bytes, "utf-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    // 获取cpu序列号
    public static String getCpuSerial() {
        String serial = null;
        try {
            OperatingSystemMXBean operatingSystemMXBean = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
            String os = operatingSystemMXBean.getName();
            if (os.contains("Windows")) {
                Process process = Runtime.getRuntime().exec(new String[]{"wmic", "cpu", "get", "ProcessorId"});
                process.getOutputStream().close();
                Scanner scanner = new Scanner(process.getInputStream());
                scanner.next();
                serial = scanner.next();
                scanner.close();
            } else if (os.contains("Mac")) {
                Process process = Runtime.getRuntime().exec(new String[]{"/usr/sbin/system_profiler", "SPHardwareDataType"});
                process.getOutputStream().close();
                Scanner scanner = new Scanner(process.getInputStream());
                while (scanner.hasNext()) {
                    String line = scanner.nextLine().trim();
                    if (line.startsWith("Serial Number")) {
                        serial = line.split(":")[1].trim();
                        break;
                    }
                }
                scanner.close();
            } else if (os.contains("Linux")) {
                Process process = Runtime.getRuntime().exec(new String[]{"dmidecode", "-t", "system"});
                process.getOutputStream().close();
                Scanner scanner = new Scanner(process.getInputStream());
                while (scanner.hasNext()) {
                    String line = scanner.nextLine().trim();
                    if (line.startsWith("Serial Number")) {
                        serial = line.split(":")[1].trim();
                        break;
                    }
                }
                scanner.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return serial;
    }

    /**
     * 获取可靠的机器码 - 主要方法
     * 使用多层次备用方案，确保100%能获取到唯一的机器码
     *
     * @return 32位十六进制机器码字符串
     */
    public static String getReliableMachineCode() {
        // 如果已经缓存，直接返回
        if (cachedMachineCode != null && !cachedMachineCode.isEmpty()) {
            return cachedMachineCode;
        }

        StringBuilder machineCode = new StringBuilder();

        try {
            // 第一优先级：稳定的硬件指纹（使用OSHI库）
            String hardwareFingerprint = getStableHardwareFingerprint();
            if (hardwareFingerprint != null && !hardwareFingerprint.isEmpty()) {
                machineCode.append("HW:").append(hardwareFingerprint);
            }

            // 第二优先级：稳定的系统特征
            String systemFingerprint = getStableSystemFingerprint();
            if (systemFingerprint != null && !systemFingerprint.isEmpty()) {
                machineCode.append("SYS:").append(systemFingerprint);
            }

            // 最后备用：最基本的稳定信息
            if (machineCode.length() == 0) {
                machineCode.append(SystemUtil.getCpuSerial());
            }

            // 生成SHA-256哈希作为最终机器码
            cachedMachineCode = generateSHA256Hash(machineCode.toString());
            return cachedMachineCode;

        } catch (Exception e) {
            e.printStackTrace();
            cachedMachineCode = generateSHA256Hash(SystemUtil.getCpuSerial());
            return cachedMachineCode;
        }
    }

    /**
     * 使用OSHI库获取稳定的硬件指纹（只包含不会变的硬件特征）
     *
     * @return 稳定的硬件指纹字符串
     */
    private static String getStableHardwareFingerprint() {
        try {
            SystemInfo systemInfo = new SystemInfo();
            HardwareAbstractionLayer hardware = systemInfo.getHardware();
            StringBuilder fingerprint = new StringBuilder();

            // 主板信息（最稳定的硬件特征）
            ComputerSystem computerSystem = hardware.getComputerSystem();
            String manufacturer = computerSystem.getManufacturer();
            String model = computerSystem.getModel();
            String serialNumber = computerSystem.getSerialNumber();

            if (manufacturer != null && !manufacturer.trim().isEmpty() &&
                    !manufacturer.equalsIgnoreCase("unknown")) {
                fingerprint.append("MANU:").append(manufacturer.trim());
            }
            if (model != null && !model.trim().isEmpty() &&
                    !model.equalsIgnoreCase("unknown")) {
                fingerprint.append("MODEL:").append(model.trim());
            }
            if (serialNumber != null && !serialNumber.trim().isEmpty() &&
                    !serialNumber.equalsIgnoreCase("unknown") &&
                    !serialNumber.equalsIgnoreCase("to be filled by o.e.m.") &&
                    !serialNumber.equalsIgnoreCase("default string")) {
                fingerprint.append("SERIAL:").append(serialNumber.trim());
            }

            // 主板信息
            Baseboard baseboard = computerSystem.getBaseboard();
            String baseboardManufacturer = baseboard.getManufacturer();
            String baseboardModel = baseboard.getModel();
            String baseboardSerial = baseboard.getSerialNumber();

            if (baseboardManufacturer != null && !baseboardManufacturer.trim().isEmpty() &&
                    !baseboardManufacturer.equalsIgnoreCase("unknown")) {
                fingerprint.append("BMANU:").append(baseboardManufacturer.trim());
            }
            if (baseboardModel != null && !baseboardModel.trim().isEmpty() &&
                    !baseboardModel.equalsIgnoreCase("unknown")) {
                fingerprint.append("BMODEL:").append(baseboardModel.trim());
            }
            if (baseboardSerial != null && !baseboardSerial.trim().isEmpty() &&
                    !baseboardSerial.equalsIgnoreCase("unknown") &&
                    !baseboardSerial.equalsIgnoreCase("default string")) {
                fingerprint.append("BSERIAL:").append(baseboardSerial.trim());
            }

            // CPU信息（稳定的处理器标识）
            CentralProcessor processor = hardware.getProcessor();
            CentralProcessor.ProcessorIdentifier processorId = processor.getProcessorIdentifier();
            String processorIdentifier = processorId.getIdentifier();
            String processorID = processorId.getProcessorID();

            if (processorIdentifier != null && !processorIdentifier.trim().isEmpty()) {
                fingerprint.append("CPUID:").append(processorIdentifier.trim());
            }
            if (processorID != null && !processorID.trim().isEmpty()) {
                fingerprint.append("CPUPID:").append(processorID.trim());
            }

            return fingerprint.length() > 0 ? fingerprint.toString() : null;

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取稳定的系统指纹（只包含真正不会变的系统特征）
     *
     * @return 稳定的系统指纹字符串
     */
    private static String getStableSystemFingerprint() {
        try {
            StringBuilder fingerprint = new StringBuilder();

            // 操作系统架构（基本不会变）
            String osArch = System.getProperty("os.arch", "");
            if (!osArch.isEmpty()) {
                fingerprint.append("ARCH:").append(osArch);
            }

            // 操作系统名称（基本不会变）
            String osName = System.getProperty("os.name", "");
            if (!osName.isEmpty()) {
                fingerprint.append("OS:").append(osName);
            }

            // 处理器核心数（除非换CPU否则不会变）
            fingerprint.append("CORES:").append(Runtime.getRuntime().availableProcessors());

            return fingerprint.length() > 0 ? fingerprint.toString() : null;

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 生成SHA-256哈希
     *
     * @param input 输入字符串
     * @return 32位十六进制哈希字符串
     */
    private static String generateSHA256Hash(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder();

            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString().toUpperCase();

        } catch (NoSuchAlgorithmException | java.io.UnsupportedEncodingException e) {
            e.printStackTrace();
            // 如果SHA-256不可用，使用MD5作为备用
            return DigestUtils.md5Hex(input).toUpperCase();
        }
    }

    /**
     * 清除机器码缓存（用于测试或重新生成）
     */
    public static void clearMachineCodeCache() {
        cachedMachineCode = null;
    }

}
