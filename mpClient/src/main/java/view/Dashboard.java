package view;

import com.google.common.primitives.Bytes;
import com.teamdev.jxbrowser.js.JsFunctionCallback;
import com.teamdev.jxbrowser.js.internal.JsPromiseImpl;
import com.teamdev.jxbrowser.navigation.event.LoadFinished;
import com.teamdev.jxbrowser.profile.Profile;
import env.Channel;
import env.Global;
import javafx.animation.TranslateTransition;
import javafx.application.Platform;
import javafx.scene.Node;
import javafx.scene.Scene;
import javafx.scene.control.ProgressBar;
import javafx.scene.layout.BorderPane;
import javafx.scene.layout.Pane;
import javafx.scene.paint.Color;
import javafx.stage.Stage;
import javafx.util.Duration;
import mgr.BrowserManager;
import tool.DownloadThreadPool;
import tool.SystemUtil;

import java.awt.*;
import java.awt.event.ActionListener;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

public class Dashboard extends Base {
    // 代码
    private byte[] code;
    // 功能
    private byte[] function;

    private TrayIcon trayIcon;

    public Map<String, Game> games;

    public Dashboard() {
        super();
        if (Global.dashboard == null) {
            Global.dashboard = this;
        }
        this.width = 360;
        this.height = 480;
        this.title = "启动器";
        this.proName = "dashboard_";
        this.games = new ConcurrentHashMap<>();
        // 开启调试模式
        this.debug = true;
        this.url = "http://localhost:5173/";
    }

    @Override
    public void init() throws Exception {
        super.init();
    }

    @Override
    public void start(Stage primaryStage) throws Exception {
        this.onInit();
        if (Global.remote_http && Objects.equals(this.proName, "dashboard_")) {
            this.url = "dashboard://dashboard/index.html";
        }
        if (this.url.equals("")) {
            System.exit(-495);
        }
        Platform.setImplicitExit(false);
        stage = primaryStage;
        // stage.initStyle(StageStyle.TRANSPARENT);
        root = new BorderPane();
        stage.setResizable(resizeable);
        scene = new Scene(root, this.width, this.height, Color.TRANSPARENT);
        // 设置图标
        javafx.scene.image.Image image = new javafx.scene.image.Image("/shijie.jpg");
        stage.getIcons().add(image);
        stage.setTitle(this.title);
        stage.setScene(scene);
        // 位置（主要是debug窗口）
        if (this.initX != -1024 || this.initY != -1024) {
            stage.setX(this.initX);
            stage.setY(this.initY);
        }
        // debug的关闭还是走这个方法
        //            System.out.println("当前窗口: " + this.getClass().getName());
        stage.setOnCloseRequest(this::onClose);
        stage.show();
        if (this instanceof Debug) {
            this.loadBrowser(root);
            return;
        }
        Global.loading = new MyProgress(root);
        new Thread(() -> {
            try {
                Global.setLoadingText("正在初始化引擎...");
                Global.startLoading();

                BrowserManager.initEngineOptions();
                BrowserManager.createEngine();
            } catch (Exception e) {
                e.printStackTrace();
                Global.setLoadingText("引擎初始化失败，请使用管理员运行！");
                try {
                    Thread.sleep(3000);
                    System.exit(-495);
                } catch (InterruptedException ex) {
                    throw new RuntimeException(ex);
                }
            }
            this.loadBrowser(root);
            this.createTray(stage);
        }).start();
    }

    @Override
    public void onLoadFinished(LoadFinished loadFinished) {
        super.onLoadFinished(loadFinished);
        if (!(this instanceof Debug)) {
            Global.loading.cancelProgressBar();
        }
    }

    @Override
    public void createBrowser() {
        Profile pro = this.checkAndGetProfile();
        this.browser = pro.newBrowser();
        browser.settings().allowJavaScriptAccessClipboard();
    }

    public void checkLocalLogic() {
        if (Global.local_logic) {
            this.code = this.local("logic.js").getBytes(StandardCharsets.UTF_8);
            this.function = this.local("ware.json").getBytes(StandardCharsets.UTF_8);
        }
    }

    @Override
    public void initEvt() {
        if (this instanceof Debug) {
            super.initEvt();
            return;
        }
        this.writeEvt();
        if (Global.local_logic) {
            this.checkLocalLogic();
            super.initEvt();
            return;
        }
        // 下载代码
        if (this.code == null || this.code.equals("")) {
            this.reqResource(Global.logicFileUrl(), (result) -> this.code = result);
            this.reqResource(Global.functionFileUrl(), (result) -> this.function = result);

        }
        super.initEvt();
    }

    private void reqResource(String url, Consumer<byte[]> cus) {
        String localUrl = url;
        if (localUrl.startsWith(Global.host)) {
            localUrl = localUrl.replace(Global.host, "");
        }
        if (localUrl.contains("?")) {
            localUrl = localUrl.substring(0, localUrl.indexOf("?"));
        }
        int last = localUrl.indexOf("/");
        if (last != -1) {
            localUrl = localUrl.substring(last + 1);
        }
        Path localResourcePath = Global.getLocalResource(localUrl);
        if (localResourcePath != null) {
            try {
                //byte[] bytes = Global.magic(localResource);
//                System.out.println("读取本地资源:" + url);
                byte[] allBytes = Files.readAllBytes(localResourcePath);
                cus.accept(allBytes);
                this.writeEvt();
                return;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        JsPromiseImpl promise = (JsPromiseImpl) this.exec(this.getMainFrame(), "Events.DOWNLOAD_FILE('" + url + "', true)");
        String finalLocalUrl = localUrl;
        promise.then((result) -> {
            String txt = (String) result[0];
            byte[] bytes = Base64.getDecoder().decode(txt);
            cus.accept(bytes);
            this.writeEvt();
//            System.out.println("下载资源:" + url);
            try {
                // 保存到本地
                Path base = BrowserManager.getUserDataDir().resolve("resource");
                base.resolve(finalLocalUrl).toFile().getParentFile().mkdirs();
                InputStream inputStream = new ByteArrayInputStream(bytes);
                Files.copy(inputStream, base.resolve(finalLocalUrl), StandardCopyOption.REPLACE_EXISTING);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        });
    }

    private void writeEvt() {
        // 开启调试窗口请求
        this.appWindow.putProperty("DASHBOARD_REQ_DEBUG", (JsFunctionCallback) this::reqDebug);
        // 启动游戏前的检查
        this.appWindow.putProperty("DASHBOARD_START_GAME_CHECK", (JsFunctionCallback) this::reqStartGameCheck);
        // 启动游戏请求
        this.appWindow.putProperty("DASHBOARD_REQ_START_GAME", (JsFunctionCallback) this::reqStartGame);
        // 停止游戏请求
        this.appWindow.putProperty("DASHBOARD_REQ_STOP_GAME", (JsFunctionCallback) this::reqStopGame);
        // 删除游戏配置请求
        this.appWindow.putProperty("DASHBOARD_REQ_DEL_GAME", (JsFunctionCallback) this::reqDelGame);
        // 修改游戏zoom级别
        this.appWindow.putProperty("DASHBOARD_REQ_SET_ZOOM", (JsFunctionCallback) this::reqSetZoom);
        // 获取机器码
        this.appWindow.putProperty("DASHBOARD_REQ_GET_MACHINE", (JsFunctionCallback) this::reqGetMachine);
        // 获取指定id的游戏启动状态
        this.appWindow.putProperty("DASHBOARD_REQ_GET_GAME", (JsFunctionCallback) this::getGameInfo);
        // 指定id的游戏 执行 jsCode
        this.appWindow.putProperty("GAME_EXEC", (JsFunctionCallback) this::gameExec);
        // 下载资源，由java下载并保存
        this.appWindow.putProperty("DASHBOARD_REQ_DOWNLOAD_RESOURCE", (JsFunctionCallback) this::reqDownloadResource);
    }

    private Object gameExec(Object... args) {
        String id = (String) args[0];
        String code = (String) args[1];
        if (Objects.equals(id, "") || Objects.equals(code, "")) return null;
        if (this.games == null || this.games.size() == 0) return null;
        Game game = this.games.get(id);
        if (game == null) return null;
        return game.exec(this.getMainFrame(), code);
    }

    private String getGameInfo(Object... args) {
        String id = (String) args[0];
        if (Objects.equals(id, "")) return "";
        if (this.games == null || this.games.size() == 0) return "";
        return this.games.get(id) == null ? "" : "1";
    }

    @Override
    protected void reloadPage() {
        // 非dev环境，不允许刷新dashboard (会导致状态不同步)
        if (Global.dev) {
            this.execQuickNr(this.getMainFrame(), "Evt.emit('EVT_RELOAD')");
            this.clearFrame();
        }
    }

    @Override
    public void onKeyEvent(String key) {
        synchronized (this) {
            super.onKeyEvent(key);
        }
    }

    public String decrypt(byte[] data) {
        try {
            InputStream inputStream = new ByteArrayInputStream(data);
            byte[] r = Global.magic(inputStream);
            return new String(r, StandardCharsets.UTF_8);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public String reqDelGame(Object... args) {
        return "";
    }

    private String reqDebug(Object... args) {
        return "";
    }

    private String reqSetZoom(Object... args) {
        if (args.length != 2) return "";
        String id = (String) args[0];
        double zoom = Double.parseDouble(String.valueOf(args[1]));
        Game game = this.games.get(id);
        if (game == null) return "";
        game.setZoom(zoom);
        return "ok";
    }

    private String reqStartGame(Object... args) {
        Platform.runLater(() -> {
            String id = "";
            try {
                id = (String) args[0];
                String url = (String) args[1];
                int channel = Integer.parseInt((String) args[2]);
                String name = (String) args[3];
                Channel target = Channel.of(channel);
                Game game = new Game();
                this.games.put(id, game);
                game.configName = name;
                if (!name.equals("")) {
                    game.title = name;
                }
                game.setId(id)
                        .setUrl(url)
                        .setChannel(target)
                        .start(new Stage());
            } catch (Exception e) {
                e.printStackTrace();
                if (!id.equals("")) {
                    // 通知启动失败
                    this.execQuickNr(this.getMainFrame(), "Events._launch_result_" + id + "(-1)");
                }
            }
        });
        return "0";
    }

    private String reqStopGame(Object... args) {
        Platform.runLater(() -> {
            String id = "";
            try {
                id = (String) args[0];
                Game game = this.games.get(id);
                if (game != null) {
                    game.onClose(null);
                    this.execQuickNr(this.getMainFrame(), "Events._stop_result_" + id + "(0)");
                    return;
                }
                this.execQuickNr(this.getMainFrame(), "Events._stop_result_" + id + "(-2)");
            } catch (Exception e) {
                e.printStackTrace();
                if (!id.equals("")) {
                    // 通知停止失败
                    this.execQuickNr(this.getMainFrame(), "Events._stop_result_" + id + "(-1)");
                }
            }
        });
        return "0";
    }

    public String getCode() {
        return !Global.local_logic ? this.decrypt(this.code) : new String(this.code, StandardCharsets.UTF_8);
    }

    public String getFunction() {
        return !Global.local_logic ? this.decrypt(this.function) : new String(this.function, StandardCharsets.UTF_8);
    }

    public String local(String url) {
        url = Paths.get("").toAbsolutePath().resolve("src").resolve("test").resolve(url).toFile().getPath();
        InputStream systemResourceAsStream = null;
        try {
            systemResourceAsStream = Files.newInputStream(Paths.get(url));
            List<Byte> iis = new ArrayList<>();
            int temp = 0;
            //当没有读取完时，继续读取
            while ((temp = systemResourceAsStream.read()) != -1) {
                iis.add((byte) (temp));
            }
            byte[] bytes = Bytes.toArray(iis);
            return new String(bytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                assert systemResourceAsStream != null;
                systemResourceAsStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return "";
    }

    public void createTray(final Stage stage) {
        if (SystemTray.isSupported()) {
            SystemTray tray = SystemTray.getSystemTray();
            java.awt.Image image = Toolkit.getDefaultToolkit().getImage(Dashboard.class.getResource("/shijie.jpg"));

            stage.setOnCloseRequest(t -> hide(t, stage));
            ActionListener closeListener = (e) -> Platform.runLater(this::doClose);
            ActionListener showListener = e -> Platform.runLater(this::show);
            PopupMenu popup = new PopupMenu();
            MenuItem showItem = new MenuItem("Open");
            showItem.addActionListener(showListener);
            popup.add(showItem);
            MenuItem closeItem = new MenuItem("Exit");
            closeItem.addActionListener(closeListener);
            popup.add(closeItem);
            trayIcon = new TrayIcon(image, "世界Online桌面版", popup);
            trayIcon.setImageAutoSize(true);
            trayIcon.addActionListener(showListener);
            try {
                tray.add(trayIcon);
            } catch (AWTException e) {
                e.printStackTrace();
            }
        }
    }

    // 关闭窗口
    public void doClose() {
        Platform.runLater(() -> {
            if (!(this instanceof Debug)) {
                if (appWindow == null) {
                    super.onClose(null);
                    return;
                }
                this.show();
                // 弹窗提示
                appWindow.putProperty("REQ_EXIT_APPLICATION", (JsFunctionCallback) (Object... args) -> {
                    Platform.runLater(() -> {
                        if (this.games != null) {
                            // 关闭所有游戏 不在dashboard的doClose关闭，是防止意外kill
                            this.games.forEach((key, value) -> value.onClose(null));
                        }
                        super.onClose(null);
                    });
                    return "";
                });
                this.simpleNotify("'{\"title\":\"退出\",\"msg\":\"将要关闭所有游戏并退出程序，是否继续？\",\"type\":\"warning\",\"maskClosable\":false,\"action\":[{\"label\":\"确认\",\"type\":\"error\",\"callback\":\"dlg.destroy();REQ_EXIT_APPLICATION()\"},{\"label\":\"取消\",\"type\":\"info\",\"callback\":\"dlg.destroy();\"}]}'");
            }
        });
    }

    private void hide(javafx.stage.WindowEvent e, final Stage stage) {
        Platform.runLater(() -> {
            if (SystemTray.isSupported()) {
                this.root.setCenter(null);
                this.initX = stage.getX();
                this.initY = stage.getY();
                stage.hide();
            } else {
                this.onClose(null);
            }
        });
        e.consume();
    }

    public void show() {
        if (!this.stage.isShowing() && (this.initX != -1024 || this.initY != -1024)) {
            this.stage.setX(this.initX);
            this.stage.setY(this.initY);
        }

        if (this.root.getCenter() == null) {
            this.root.setCenter(this.browserView);
        }
        if (!this.stage.isShowing()) {
            this.stage.show();
        }
        this.stage.requestFocus();
        this.stage.toFront();
    }

    private String reqGetMachine(Object... args) {
        return SystemUtil.getCpuSerial();
    }

    public Pane loading() {
        Pane loading = new Pane();
        double w = 360, h = 480;
        ProgressBar bar = new ProgressBar();
        bar.setMinWidth(200);
        bar.setPrefWidth(200);
        bar.setLayoutX(w / 2 - bar.getMinWidth() / 2);
        bar.setLayoutY(h / 2);
        loading.getChildren().add(bar);
        loading.setLayoutX(0);
        loading.setLayoutY(0);
        loading.setPrefWidth(w);
        loading.setPrefHeight(h);
        return loading;
    }

    protected TranslateTransition getAnimation(Node node) {
        TranslateTransition translateTransition = new TranslateTransition();
        translateTransition.setByY(190);
        translateTransition.setDuration(Duration.millis(400));
        translateTransition.setCycleCount(1);
        translateTransition.setAutoReverse(false);
        translateTransition.setNode(node);
        return translateTransition;
    }

    public Object reqStartGameCheck(Object... args) {
        CompletableFuture.supplyAsync(() -> {
            int channel = Integer.parseInt((String) args[0]);
            Channel target = Channel.of(channel);
            if (target == Channel.NONE) {
                this.execQuickNr(this.getMainFrame(), "GAME_CHECK_RESPONSE('错误的渠道', [])");
                return null;
            }
            try {
                List<String> files = Global.getGameFiles(target);
                if (files.isEmpty()) {
                    this.execQuickNr(this.getMainFrame(), "GAME_CHECK_RESPONSE('', [])");
                    return null;
                }
                StringBuilder sb = new StringBuilder();
                for (String file : files) {
                    sb.append("'").append(file).append("'").append(",");
                }
                this.execQuickNr(this.getMainFrame(), "GAME_CHECK_RESPONSE('', [" + sb + "])");
                return null;
            } catch (Exception e) {
                this.execQuickNr(this.getMainFrame(), "GAME_CHECK_RESPONSE('远程服务器错误，下载资源失败!', [])");
                return null;
            }
        });
        return "";
    }

    public Object reqDownloadResource(Object... args) {
        String path = (String) args[0];
        int channel = Integer.parseInt((String) args[1]);
        Channel target = Channel.of(channel);
        if (target == Channel.NONE) {
            return null;
        }
        String url = Global.host + "yxmxt/" + target.getSymbol() + "/" + path;
        Path base = Global.getLocalResourceDir().resolve(target.getUserDataPath());
        DownloadThreadPool.getInstance().submitDownload(path, url, base, path, (downloadUnique, result) -> {
            this.execQuickNr(this.getMainFrame(), "DOWNLOAD_RESPONSE('" + downloadUnique + "', '" + result + "')");
        });
        return "";
    }

}

