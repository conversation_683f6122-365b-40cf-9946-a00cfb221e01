package view;

import com.teamdev.jxbrowser.frame.Frame;
import com.teamdev.jxbrowser.js.JsFunctionCallback;
import com.teamdev.jxbrowser.media.event.AudioStartedPlaying;
import com.teamdev.jxbrowser.navigation.event.LoadFinished;
import com.teamdev.jxbrowser.navigation.event.NavigationFinished;
import com.teamdev.jxbrowser.os.Environment;
import com.teamdev.jxbrowser.profile.Profile;
import com.teamdev.jxbrowser.zoom.ZoomLevel;
import env.Channel;
import env.Global;
import javafx.application.Platform;
import javafx.stage.Stage;
import javafx.stage.WindowEvent;
import msg.GameMsg;
import net.MyNetworkDelegate;
import org.apache.commons.codec.digest.DigestUtils;
import tool.Listener;
import tool.DownloadThreadPool;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class Game extends Base {
    // 默认宽高
    public static int defaultWidth = 834;
    public static int defaultHeight = 1112;

    // 对应的配置id
    private String id;
    private Listener listener;
    // 回消息
    private boolean responseLaunch = false;
    // 配置的名称，启动的时候发送过来
    public String configName = "";
    public Channel channel = Channel.NONE;

    public Game() {
        super();
        if (this.width == 0 && this.height == 0) {
            this.width = defaultWidth;
            this.height = defaultHeight;
            if (Environment.isMac()) {
                this.width = 375;
                this.height = 667;
            }
        }
        this.title = "";
        this.debug = true;
        this.url = "http://localhost:5174/";
        this.resizeable = false;
    }

    @Override
    public String getId() {
        return id;
    }

    public Game setId(String id) {
        this.id = id;
        this.proName = "game_" + id;
        return this;
    }

    public Game setUrl(String url) {
        this.url = url;
        return this;
    }

    public Game setChannel(Channel channel) {
        if (channel == Channel.NONE) {
            throw new IllegalArgumentException("渠道不正确");
        }
        this.channel = channel;
        return this;
    }

    /**
     * 获取游戏资源根目录
     */
    public Path getResourceDir() {
        Path base = Global.getLocalResourceDir().resolve(this.channel.getUserDataPath());

        return base;
    }

    /**
     * 获取游戏资源目录
     */
    public Path getResourceDir(String url) {
        String gameDir = this.channel.getUserDataPath();
        if (url.startsWith("/")) {
            url = url.substring(1);
        }
        if (!url.contains(gameDir)) {
            url = gameDir + "/" + url;
        }
        Path base = Global.getLocalResourceDir().resolve(url);
        return base;
    }


    @Override
    public void start(Stage primaryStage) throws Exception {
        // 获取zoom配置
        String zoom = (String) Global.dashboard.exec(Global.dashboard.getMainFrame(), "Events.GetZoom('" + this.getId() + "');");
        System.out.println("game init zoom:" + zoom);
        this.zoom = Double.parseDouble(zoom);
        super.start(primaryStage);
    }

    @Override
    public void createBrowser() {
        if (Objects.equals(this.id, "")) {
            System.out.println("game 的配置id未被设置，无法创建浏览器");
            return;
        }
        Profile pro = this.checkAndGetProfile();
        this.browser = pro.newBrowser();
        browser.settings().allowJavaScriptAccessClipboard();
        // 静音模式
        browser.audio().on(AudioStartedPlaying.class, evt -> {
            evt.audio().mute();
        });
        // 开启网络req拦截
        MyNetworkDelegate networkDelegate = new MyNetworkDelegate(pro.network(), this.channel);
        networkDelegate.doDelegate();
    }


    @Override
    public void initEvt() {
        gameWindow.putProperty("REQ_JAVA_LOAD_LOGIC", (JsFunctionCallback) this::loadLogic);
        gameWindow.putProperty("SET_JAVA_APP_TITTLE", (JsFunctionCallback) this::changeTitle);
        gameWindow.putProperty("READ_FILE_SYNC", (JsFunctionCallback) this::readFileSync);
        gameWindow.putProperty("MAKE_DIR_SYNC", (JsFunctionCallback) this::mkdirSync);
        gameWindow.putProperty("WRITE_FILE_SYNC", (JsFunctionCallback) this::writeFileSync);
        gameWindow.putProperty("LOAD_GAME_SCRIPT", (JsFunctionCallback) this::loadGameScript);
        gameWindow.putProperty("DOWNLOAD_FILE", (JsFunctionCallback) this::downloadFile);
        gameWindow.putProperty("ACCESS_FILE", (JsFunctionCallback) this::accessFile);
        gameWindow.putProperty("COPY_FILE", (JsFunctionCallback) this::copyFile);
        gameWindow.putProperty("WINDOW_SIZE", (JsFunctionCallback) this::windowSize);
        gameWindow.putProperty("USER_DATA_PATH", (JsFunctionCallback) this::getUserDataPath);

        appWindow.putProperty("REQ_JAVA_LOAD_FUNCTION", (JsFunctionCallback) this::loadFunction);
        appWindow.putProperty("DASHBOARD_EXEC", (JsFunctionCallback) this::dashboardExec);
        appWindow.putProperty("RELOAD_CALL_BACK", (JsFunctionCallback) this::reloadCallBack);
        super.initEvt();
    }

    @Override
    protected void reloadPage() {
        this.execQuickNr(this.getMainFrame(), "Evt.emit('EVT_RELOAD', `RELOAD_CALL_BACK && RELOAD_CALL_BACK()`)");
    }

    // 提供给game客户端 调用dashboard的方法
    private Object dashboardExec(Object... args) {
        String jsCode = String.valueOf(args[0]);
        if (jsCode.equals("")) {
            return null;
        }
        return Global.dashboard.exec(Global.dashboard.getMainFrame(), jsCode);
    }

    private String reloadCallBack(Object... args) {
        if (Global.debug && Global.local_logic) {
            // dev环境下，重新加载logic
            Global.dashboard.checkLocalLogic();
        }
        this.execQuickNr(this.getMainFrame(), "location.href = \"" + this.url + "\"");
        this.clearFrame();
        return "";
    }

    // 注入代码
    private String loadLogic(Object... args) {
        String code = Global.dashboard.getCode();
        this.exec(this.getGameFrame(), code);
        this.execQuickNr(this.getGameFrame(), "loadGameScript();");
        this.sendMsg(GameMsg.LOGIC_INJECT_DONE);
        return "";
    }

    private String loadFunction(Object... args) {
        return Global.dashboard.getFunction();
    }

    // 修改窗口标题
    private String changeTitle(Object... args) {
        Platform.runLater(() -> {
            String name = " [ " + args[0] + " ] ";
            if (!this.configName.equals("")) {
                name = this.configName + "-" + name;
            }
            this.stage.titleProperty().setValue(name);
            if (this.debugWindow != null)
                this.debugWindow.stage.titleProperty().setValue(" [ " + args[0] + " ] ");
        });
        return "";
    }

    // evt 派发
    public void sendMsg(GameMsg code) {
        this.execQuickNr(this.getMainFrame(), "Events.onMsg(" + code.ordinal() + ")");
    }

    @Override
    public void onLoadFinished(LoadFinished loadFinished) {
        super.onLoadFinished(loadFinished);
        // 放入id参数
        appWindow.putProperty("GAME_ID", this.id);
        appWindow.putProperty("GAME_CONFIG_NAME", this.configName);
        // 进行一次 zoom 事件
        this.execQuickNr(this.getMainFrame(), "typeof ON_ZOOM_EVT != 'undefined' && ON_ZOOM_EVT(" + this.zoom + ")");
        this.browser.zoom().level(ZoomLevel.P_100);
        if (!this.responseLaunch) {
            this.notifyLaunchSuccess();
            this.responseLaunch = true;
        }
        this.execQuickNr(this.getGameFrame(), "(async function(){const sid=setInterval(()=>{if(window.REQ_JAVA_LOAD_LOGIC!=null){window.REQ_JAVA_LOAD_LOGIC();clearInterval(sid)}},100)})()");
    }

    public void onKeyEvent(String key) {
        synchronized (this) {
            super.onKeyEvent(key);
        }
    }

    @Override
    public void onNavigationFinished(NavigationFinished navigationFinished) {
        super.onNavigationFinished(navigationFinished);
    }

    @Override
    public void onClose(WindowEvent event) {
        try {
            if (this.debugWindow != null) {
                this.debugWindow.onClose(event);
            }
            if (this.listener != null && this.listener.isAlive()) {
                this.listener.run = false;
            }
            if (event != null) {
                this.notifyStop();
            }
            this.setPositionOnClose();
            this.setSceneSizeOnClose();
            // 重置zoom率
            this.browser.zoom().reset();
            this.browser.close();
            this.stage.close();
            Global.dashboard.games.remove(this.id);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 通知启动成功了
     */
    public void notifyLaunchSuccess() {
        Global.dashboard.execQuickNr(Global.dashboard.getMainFrame(), "Events._launch_result_" + id + "(0)");
    }

    /**
     * 通知停止game
     */
    public void notifyStop() {
        Global.dashboard.execQuickNr(Global.dashboard.getMainFrame(), "Events._stop_result_" + id + "(1)");
    }


    /**
     * 设置zoom 暂时不需要了
     */
    public void setZoom(double z) {
        if (this.zoom == z) return;
        this.zoom = z;
        this.execQuickNr(this.getMainFrame(), " typeof ON_ZOOM_EVT != 'undefined' && ON_ZOOM_EVT(" + z + ")");
    }

    /**
     * 读取文件
     */
    public Object readFileSync(Object... args) {
        try {
            String url = (String) args[0];
            String encoding = (String) args[1];
            System.out.println("readFileSync:" + url + ", " + encoding);
            Path path = this.getResourceDir(url);
            // 任何时候都要核验部分文件md5, 防止文件被篡改
            Map<String, String> md5Map = Global.channelMd5Map.computeIfAbsent(this.channel, k -> new HashMap<>());
            String md5 = md5Map.get(url);
            if (md5 != null && !Objects.equals(md5, "")) {
                InputStream inputStream = Files.newInputStream(path);
                String md5Hex = DigestUtils.md5Hex(inputStream);
                if (!Objects.equals(md5, md5Hex)) {
                    System.out.println("文件疑似被篡改:" + url);
                    System.exit(-601);
                }
            }
            byte[] buffer = Files.readAllBytes(path.toAbsolutePath());
            if (Objects.equals(encoding, "utf8")) {
                return new String(buffer, StandardCharsets.UTF_8);
            }
            return buffer;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public Object mkdirSync(Object... args) {
        try {
            String url = (String) args[0];
            boolean recursive = (boolean) args[1];
            File file = this.getResourceDir(url).toFile();
            if (!file.exists()) {
                if (recursive) {
                    return file.mkdirs();
                } else {
                    return file.mkdir();
                }
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public Object writeFileSync(Object... args) {
        try {
            String filePath = args[0].toString();
            String data = args[1].toString();
            String encoding = args[2].toString();
            // 处理不同类型的数据
            byte[] bytesToWrite;
            if ("base64".equals(encoding)) {
                // Base64解码
                bytesToWrite = java.util.Base64.getDecoder().decode(data);
            } else {
                // UTF-8或其他文本编码
                bytesToWrite = data.getBytes(java.nio.charset.StandardCharsets.UTF_8);
            }

            Path to = this.getResourceDir(filePath);
            File file = to.toFile();
            java.io.File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            // 写入文件
            try (java.io.FileOutputStream fos = new java.io.FileOutputStream(file)) {
                fos.write(bytesToWrite);
                fos.flush();
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public Object loadGameScript(Object... args) {
        String val = this.readFileSync(args).toString();
        if (val != null) {
            this.exec(this.getGameFrame(), val);
        }
        return null;
    }

    public Object downloadFile(Object... args) {
        try {
            String unique = args[0].toString();
            String url = args[1].toString();
            System.out.println("收到下载请求 - unique: " + unique + ", url: " + url);
            Path base = this.getResourceDir();
            // 提交到线程池进行异步下载
            DownloadThreadPool.getInstance().submitDownload(unique, url, base, "", (downloadUnique, result) -> {
                // 下载完成后回调前端
                this.execQuickNr(this.getGameFrame(), "wx.onDownloadResult('" + downloadUnique + "', '" + result + "')");
            });
            return "";
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public Object accessFile(Object... args) {
        String url = args[0].toString();
        Path path = this.getResourceDir(url);
        return path.toFile().exists();
    }


    public Object copyFile(Object... args) {
        String srcPath = args[0].toString();
        String destPath = args[1].toString();

        Path source = this.getResourceDir(srcPath);
        Path destination = this.getResourceDir(destPath);
        try {
            Files.copy(source, destination);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public Object windowSize(Object... args) {
        return this.width + "," + this.height;
    }

    public Object getUserDataPath(Object... args) {
        return this.channel.getUserDataPath();
    }

}
