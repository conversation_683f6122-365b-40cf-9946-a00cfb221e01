package env;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import javafx.application.Platform;
import javafx.scene.Node;
import mgr.BrowserManager;
import net.NetworkManager;
import net.server.Server;
import org.apache.commons.codec.digest.DigestUtils;
import tool.SevenZip;
import view.Dashboard;
import view.MyProgress;

import java.awt.*;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class Global {
    public final static int CHANNEL_NONE = -1;
    // 小程序
    public final static int MINI_PROGRAM = 1;

    final static String property = System.getProperty("user.dir");
    public final static String type_dashboard = "dashboard";
    public final static String type_game = "game";
    public static Dashboard dashboard;
    public static MyProgress loading;

    public static String VER_STR = "development";
    public static int VERSION = 410;

    // 前端代码使用game://game/index.html 线上模式
    // 打包时切换成true false false
    public static boolean remote_http = false;
    // 开发者模式
    public static boolean dev = true;
    public static boolean debug = true;
    // 使用本地代码逻辑
    public static boolean local_logic = true;

    // 这个md5是脚本 + index.bin
    static HashMap<String, String> md5Map = new HashMap<>();
    final static Map<String, String> md5 = new ConcurrentHashMap<>();
    public static String host = "https://husong.vip/";
    public static final String min_login_js = "_min.logic.js";
    public static final String ware_json = "_ware.json";

    public static Map<Channel, Map<String, String>> channelMd5Map = new HashMap<>();

    public static String logicFileUrl() {
        return host + "worldh5/" + min_login_js + "?" + System.currentTimeMillis();
    }


    public static String functionFileUrl() {
        return host + "worldh5/" + ware_json + "?" + System.currentTimeMillis();
    }


    public static String clientFileUrl(String type) {
        return host + "worldh5/" + type + ".7z";
    }

    public static String md5File() {
        return host + "worldh5/md5.bin";
    }

    static {
        // 退出监听
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("程序退出，清理文件。");
            Server server = NetworkManager.getMe().getServer();
            if (server != null) {
                server.release();
            }
            try {
                Path base = Paths.get(property);
                Paths.get(base + "/" + type_dashboard + ".7z").toFile().delete();
                Paths.get(base + "/" + type_game + ".7z").toFile().delete();
                System.out.println("清理完成。");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }));
    }

    public static void setLoadingText(String v) {
        if (Global.loading != null) {
            Global.loading.setText(v);
        }
    }

    public static void startLoading() {
        Platform.runLater(() -> {
            if (Global.loading != null) {
                Global.loading.activateProgressBar();
            }
        });
    }

    public static void stopLoading() {
        Platform.runLater(() -> {
            if (Global.loading != null) {
                Global.loading.cancelProgressBar();
            }
        });
    }

    /**
     * 获取本地资源路径
     */
    public static Path getLocalResourceDir() {
        return BrowserManager.getUserDataDir().resolve("resource");
    }

    public static String remoteHost() {
        return "https://h5.javaelf.cn";
    }

    public static String getHost() {
        return dev ? "http://localhost:9001" : Global.remoteHost();
    }

    public static Path getLocalResource(String url) {
        try {
            System.out.println("getLocalResource:" + url);
            Path base = Global.getLocalResourceDir();
            Path path = base.resolve(url);
            if (!path.toFile().exists()) {
                return null;
            }
            InputStream inputStream = Files.newInputStream(path.toFile().toPath());
            // 检查md5
            if (md5Map.size() == 0) {
                getMd5Info();
            }

            if (url.endsWith("index.bin")) {
                url = "v5/" + url;
            }

            if (!md5Map.containsKey(url)) {
                return null;
            }

            String md5 = md5Map.get(url);
            // 为文件生成md5
            String md5Hex = DigestUtils.md5Hex(inputStream);
            if (!md5.equals(md5Hex)) {
                System.out.println("md5不一致，需要重新下载:" + url);
                return null;
            }
            System.out.println("md5一致，不需要重新下载:" + url);
            return path.toFile().toPath();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static InputStream getLocalResourceAsStream(String url) {
        try {
            return Files.newInputStream(Objects.requireNonNull(getLocalResource(url)));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    // 获取logic/ware/index.bin等需要频繁热更的文件
    // 先从本地获取，并且判断md5，如果md5不一致，再从服务器获取
    public static InputStream getHttpResource(String url) {
        InputStream inputStream = null;
        try {
            inputStream = getLocalResourceAsStream(url);
            if (inputStream != null) {
                return inputStream;
            }
            System.out.println("getHttpResource:" + url);
            URL _u = new URL(Global.host + "worldh5/v5/" + url);
            HttpURLConnection connection = (HttpURLConnection) _u.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(30000);
            connection.connect();
            int status = connection.getResponseCode();
            if (status == 200) {
                inputStream = connection.getInputStream();
            } else {
                throw new RuntimeException("获取文件出错: " + status);
            }
            // 保存文件
            Path base = BrowserManager.getUserDataDir().resolve("resource");
            base.resolve(url).toFile().getParentFile().mkdirs();
            Files.copy(inputStream, base.resolve(url), StandardCopyOption.REPLACE_EXISTING);
            return Files.newInputStream(base.resolve(url));
        } catch (Exception e) {
            e.printStackTrace();
        }

        return inputStream;
    }

    public static void getMd5Info() {
//        try {
//            String out = getHttp(getHost() + "/md5Info", "");
//            JSONObject jsonObject = JSON.parseObject(out);
//            int code = jsonObject.getIntValue("code");
//            if (code != 0) {
//                System.out.println("获取md5信息失败");
//                System.exit(-5);
//            }
//            String md5Info = jsonObject.getString("data");
//            JSONObject md5Object = JSON.parseObject(md5Info);
//            for (Map.Entry<String, Object> entry : md5Object.entrySet()) {
//                md5Map.put(entry.getKey(), entry.getValue().toString());
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            System.exit(-6);
//        }
        System.out.println("获取md5信息 需要重写");
    }

    /**
     * 获取游戏文件
     *
     * @param channel 渠道
     * @return 需要下载的游戏文件列表
     */
    public static List<String> getGameFiles(Channel channel) throws Exception {
        String host = Global.host + "yxmxt/" + channel.getSymbol() + "/filelist.txt";
        String out = getHttp(host, "");
        String[] strings = out.split(",");
        // 检查本地文件列表 看看要不要下载
        List<String> needDownload = new ArrayList<>();
        Map<String, String> md5Map = Global.channelMd5Map.computeIfAbsent(channel, k -> new HashMap<>());
        md5Map.clear();
        for (int i = 0; i < strings.length; ) {
            String filePath = strings[i++];
            String md5 = strings[i++];
            md5 = md5.replace("\n", "");
            md5Map.put(filePath, md5);
            Path path = Global.getLocalResourceDir().resolve(channel.getUserDataPath()).resolve(filePath);
            File file = path.toFile();
            if (!file.exists()) {
                needDownload.add(filePath);
                continue;
            }
            InputStream inputStream = Files.newInputStream(file.toPath());
            String md5Hex = DigestUtils.md5Hex(inputStream);
            if (!Objects.equals(md5, md5Hex)) {
                needDownload.add(filePath);
            }
        }
        return needDownload;
    }

    private static String getHttp(String url, String urlParameters) throws IOException {
        byte[] postData = urlParameters.getBytes(StandardCharsets.UTF_8);
        if (!Objects.equals(urlParameters, "")) {
            url = url + "?" + urlParameters;
        } else {
            url = url + "?time=" + System.currentTimeMillis();
        }
        URL myurl = new URL(url);
        HttpURLConnection conn = (HttpURLConnection) myurl.openConnection();
        conn.setRequestMethod("GET");
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(30000);
        conn.setUseCaches(false);
        conn.setRequestProperty("Cache-Control", "no-cache, no-store, must-revalidate");
        conn.setRequestProperty("Pragma", "no-cache");
        conn.setRequestProperty("Expires", "0");
        StringBuilder content = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = br.readLine()) != null) {
                content.append(line);
                content.append(System.lineSeparator());
            }
        }
        conn.disconnect();
        return content.toString();
    }

    public static double getScreenHeight() {
        Toolkit defaultToolkit = Toolkit.getDefaultToolkit();
        Dimension screenSize = defaultToolkit.getScreenSize();
        return screenSize.getHeight();
    }

    public static double getScreenWidth() {
        Toolkit defaultToolkit = Toolkit.getDefaultToolkit();
        Dimension screenSize = defaultToolkit.getScreenSize();
        return screenSize.getWidth();
    }

    public static byte[] magic(InputStream inputStream) throws IOException {
        byte[] magicBytes = new byte[4];
        if (inputStream.read(magicBytes) != 4) {
            throw new IOException("读取魔术数字失败");
        }
        int magicNumber = ByteBuffer.wrap(magicBytes).getInt();
        byte[] sizeBytes = new byte[8];
        if (inputStream.read(sizeBytes) != 8) {
            throw new IOException("读取文件大小失败");
        }
        long originalFileSize = ByteBuffer.wrap(sizeBytes).getLong();

        byte[] contentBytes = new byte[(int) originalFileSize];
        int bytesRead = 0;
        int offset = 0;
        // 循环读取，直到读取到所有预期的数据
        while (bytesRead < contentBytes.length) {
            int read = inputStream.read(contentBytes, offset, contentBytes.length - bytesRead);
            if (read == -1) {
                // 没有更多数据可读，可能是连接关闭或数据不完整
                throw new IOException("连接关闭或数据不完整");
            }
            bytesRead += read;
            offset += read;
        }

        // 对异或后的内容进行异或操作以还原原始内容
        byte[] data = new byte[contentBytes.length];
        for (int i = 0; i < contentBytes.length; i++) {
            data[i] = (byte) (contentBytes[i] ^ magicNumber);
        }
        return data;
    }

    public static void appendAndReplace(Node node, String style) {
        String oldStyle = node.getStyle();
        if (!style.endsWith(";")) {
            style += ";";
        }
        if (oldStyle.length() == 0) {
            node.setStyle(style);
            return;
        }
        String finalStyle = removeDuplicationStyle(node.getStyle() + style);
        //logger.info("finalStyle:{}", finalStyle);
        node.setStyle(finalStyle);
    }

    public static String removeDuplicationStyle(String style) {
        StringBuilder builder = new StringBuilder();
        HashMap<String, String> map = new HashMap<>();
        String[] split = style.split(";");
        for (String str : split) {
            if (str.equals("")) {
                continue;
            }
            String[] styleStr = str.split(":");
            if (styleStr.length != 2) {
                //logger.error("style 格式不正确:{},样式表丢失.", str);
                continue;
            }
            String key = styleStr[0];
            String value = styleStr[1];
            map.put(key, value);
        }
        if (map.size() > 0) {
            map.forEach((k, v) -> {
                builder.append(k);
                builder.append(":");
                builder.append(v);
                builder.append(";");
            });
        }
        return builder.toString();
    }

}
