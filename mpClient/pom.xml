<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.qiyou.world</groupId>
    <artifactId>world-v5</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <download.url>husong.vip</download.url>
        <jx-version>7.21</jx-version>
    </properties>
    <profiles>
        <profile>
            <id>windows</id>
            <activation>
                <os>
                    <family>windows</family>
                </os>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>com.teamdev.jxbrowser</groupId>
                    <artifactId>jxbrowser-win64</artifactId>
                    <version>${jx-version}</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>mac</id>
            <activation>
                <os>
                    <family>mac</family>
                </os>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>com.teamdev.jxbrowser</groupId>
                    <artifactId>jxbrowser-mac</artifactId>
                    <version>${jx-version}</version>
                </dependency>
            </dependencies>
        </profile>
    </profiles>
    <dependencies>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>4.1.94.Final</version>
        </dependency>
        <dependency>
            <groupId>org.tukaani</groupId>
            <artifactId>xz</artifactId>
            <version>1.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.26.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>32.0.1-android</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.15</version>
        </dependency>
        <dependency>
            <groupId>com.google.javascript</groupId>
            <artifactId>closure-compiler</artifactId>
            <version>v20220502</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
            <version>6.4.6</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty.websocket</groupId>
            <artifactId>websocket-server</artifactId>
            <version>9.4.35.v20201120</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty.websocket</groupId>
            <artifactId>websocket-servlet</artifactId>
            <version>9.4.35.v20201120</version>
        </dependency>
        <dependency>
            <groupId>cn.twomiles</groupId>
            <artifactId>xjar</artifactId>
            <scope>system</scope>
            <systemPath>${project.basedir}/libs/xjar.jar</systemPath>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.teamdev.jxbrowser</groupId>
            <artifactId>jxbrowser</artifactId>
            <version>${jx-version}</version>
        </dependency>
        <dependency>
            <groupId>com.teamdev.jxbrowser</groupId>
            <artifactId>jxbrowser-javafx</artifactId>
            <version>${jx-version}</version>
        </dependency>
    </dependencies>
    <repositories>
        <repository>
            <id>huyang</id>
            <url>http://husong.vip:8081/repository/maven-huyang/</url>
        </repository>
        <repository>
            <id>google</id>
            <url>https://mvnrepository.com/artifact/</url>
        </repository>
    </repositories>

</project>
